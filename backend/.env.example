APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=uae
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=reverb

# Laravel Reverb Configuration
REVERB_APP_ID=vitamins-app
REVERB_APP_KEY=vitamins-key
REVERB_APP_SECRET=vitamins-secret
REVERB_HOST=localhost
REVERB_PORT=8080
REVERB_SCHEME=http
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

# Real-time Notifications Configuration
NOTIFICATIONS_REALTIME_ENABLED=true
NOTIFICATIONS_DATABASE_ENABLED=true
NOTIFICATIONS_MAIL_ENABLED=true
NOTIFICATIONS_PUSH_ENABLED=false
NOTIFICATIONS_BROADCASTING_ENABLED=true
NOTIFICATIONS_QUEUE=notifications
NOTIFICATIONS_QUEUE_CONNECTION=redis
NOTIFICATIONS_QUEUE_NAME=notifications
NOTIFICATIONS_RETRY_AFTER=90
NOTIFICATIONS_MAX_TRIES=3
NOTIFICATIONS_READ_RATE_LIMIT="100,1"
NOTIFICATIONS_WRITE_RATE_LIMIT="60,1"
NOTIFICATIONS_DEVICE_RATE_LIMIT="10,1"
NOTIFICATIONS_CLEANUP_ENABLED=true
NOTIFICATIONS_KEEP_DAYS=90
NOTIFICATIONS_CLEANUP_BATCH_SIZE=1000

# Push Notifications (Optional)
FCM_ENABLED=false
FCM_SERVER_KEY=
FCM_SENDER_ID=
APNS_ENABLED=false
APNS_CERTIFICATE_PATH=
APNS_CERTIFICATE_PASSPHRASE=
APNS_PRODUCTION=false

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_URL=

# Pixabay API Configuration
PIXABAY_API_KEY=
PIXABAY_API_URL=https://pixabay.com/api/

VITE_APP_NAME="${APP_NAME}"

# Development Database Manager Credentials (Development Only)
DEV_DB_USERNAME=admin
DEV_DB_PASSWORD=dev123!@#
