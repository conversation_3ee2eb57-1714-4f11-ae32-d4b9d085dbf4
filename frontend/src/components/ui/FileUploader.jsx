import { useState, useRef } from "react";
import { uploadSingleFile } from "@/utils/fileUploadHelpers";
import LoadingSpinner from "./LoadingSpinner";
import { useTranslation } from "react-i18next";

const FileUploader = ({
  value,
  onUploadSuccess,
  onFileSuccess,
  asFile = false,
  width = "w-full",
}) => {
  const { t } = useTranslation();
  const [fileName, setFileName] = useState(value || "");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const fileInputRef = useRef(null);

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (asFile) {
      setFileName(file.name);
      onFileSuccess?.(file);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const response = await uploadSingleFile(file);
      const url = response.data?.data?.path;
      if (!url) throw new Error("No URL returned from upload");
      setFileName(file.name);
      onUploadSuccess?.(url);
      fileInputRef.current.value = "";
    } catch (err) {
      setError(err.message || "Failed to upload file");
      console.error("Upload error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  const clearFile = () => {
    setFileName("");
    onUploadSuccess?.(null);
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center relative">
        {/* Wrapper with padding-left for icon */}
        <div className="relative flex-grow" onClick={triggerFileInput}>
          {/* Left-side file icon */}
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-5 h-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M7 21h10a2 2 0 002-2V7l-5-5H7a2 2 0 00-2 2v15a2 2 0 002 2z"
              />
            </svg>
          </div>

          {/* Input with left padding for icon */}

          <input
            type="text"
            readOnly
            value={fileName}
            placeholder={t("commonPlaceholder.filePlaceholder")}
            className={`border border-gray-300 rounded pl-10 pr-8 py-2 text-sm ${width} focus:outline-none focus:ring-2 focus:ring-blue-400`}
          />

          {/* Cross button (only if file is selected) */}
          {fileName && (
            <button
              type="button"
              onClick={clearFile}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-red-500"
              aria-label="Remove file"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-4 h-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          )}
        </div>

        {/* Upload button */}
        <button
          type="button"
          onClick={triggerFileInput}
          className="flex items-center justify-center w-9 h-9 bg-gray-400 rounded hover:bg-primary-500 transition-colors"
          aria-label="Upload File"
        >
          {isLoading ? (
            <LoadingSpinner />
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-5 h-5 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M7 10l5-5m0 0l5 5m-5-5v12"
              />
            </svg>
          )}
        </button>
      </div>

      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept=".pdf,.doc,.docx,.zip,.rar,.txt"
        className="hidden"
      />

      {error && <p className="text-red-500 text-sm">{error}</p>}
    </div>
  );
};

export default FileUploader;
