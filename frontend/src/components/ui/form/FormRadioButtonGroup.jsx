import { useField } from "formik";
import { motion } from "framer-motion";

const FormRadioButtonGroup = ({
  label,
  options,
  helperText,
  className = "",
  onChange,
  ...props
}) => {
  const [field, meta, helpers] = useField(props);
  const hasError = meta.touched && meta.error;

  const handleChange = (value) => {
    helpers.setValue(value);
    if (typeof onChange === "function") {
      onChange(value);
    }
  };

  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
          {props.required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      <div className="flex flex-wrap gap-4">
        {options.map((option) => {
          const isSelected = field.value === option.value;
          return (
            <label
              key={option.value}
              htmlFor={`${props.name}-${option.value}`}
              className={`
                flex items-center gap-2 px-4 py-3 rounded cursor-pointer transition-all w-44
                ${
                  props.disabled
                    ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                    : isSelected
                    ? "bg-primary-600 text-white font-medium text-sm"
                    : "bg-gray-200 text-slate-950 font-normal text-sm"
                }
              `}
            >
              <input
                id={`${props.name}-${option.value}`}
                name={props.name}
                type="radio"
                checked={isSelected}
                onChange={() => handleChange(option.value)}
                onBlur={field.onBlur}
                disabled={props.disabled}
                className={`
                  h-4 w-4 border-gray-300 text-primary-500 focus:ring-primary-500 rounded
                  ${hasError ? "border-red-300" : ""}
                  ${props.disabled ? "cursor-not-allowed" : "cursor-pointer"}
                `}
              />
              {option.label}
            </label>
          );
        })}
      </div>

      {hasError ? (
        <motion.p
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-sm text-red-600 mt-1"
        >
          {meta.error}
        </motion.p>
      ) : helperText ? (
        <p className="text-xs text-gray-500 mt-1">{helperText}</p>
      ) : null}
    </div>
  );
};

export default FormRadioButtonGroup;
