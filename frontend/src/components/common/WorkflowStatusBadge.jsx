// status Color
const StatusColors = {
  pending: "bg-yellow-100 text-yellow-800",
  draft: "bg-gray-100 text-gray-700",
  submitted: "bg-blue-100 text-blue-800",
  open: "bg-green-100 text-green-800",
  inprogress: "bg-indigo-100 text-indigo-800",
  resolved: "bg-teal-100 text-teal-800",
  closed: "bg-red-100 text-red-800",
  published: "bg-emerald-100 text-emerald-800",
};

const WorkflowStatusBadge = ({
  data,
  fieldName = "status",
  className = "",
  t,
}) => {
  if (!data || typeof data[fieldName] === "undefined") return null;

  const rawStatus = String(data[fieldName]).trim().toLowerCase();
  const color =
    StatusColors[rawStatus] ||
    "bg-gray-100 text-gray-800 border border-gray-300";

  const label = t
    ? t(`status.${rawStatus}`) ||
      rawStatus.charAt(0).toUpperCase() + rawStatus.slice(1)
    : rawStatus.charAt(0).toUpperCase() + rawStatus.slice(1);

  return (
    <span
      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full shadow w-20 justify-center items-center ${color} ${className}`}
    >
      {label}
    </span>
  );
};

export default WorkflowStatusBadge;
