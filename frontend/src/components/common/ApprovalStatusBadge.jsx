// status Color
const StatusColors = {
  pending: "bg-amber-100 text-amber-800 border border-amber-300",
  approved: "bg-green-100 text-green-800 border border-green-300",
  rejected: "bg-rose-100 text-rose-800 border border-rose-300",
  onHold: "bg-purple-100 text-purple-800 border border-purple-300",
  cancelled: "bg-orange-100 text-orange-800 border border-orange-300",
  active: "bg-green-100 text-green-800 border border-green-300",
  inactive: "bg-red-200 text-red-800 border border-red-300",
  suspended: "bg-red-100 text-red-800 border border-red-300",
};

const ApprovalStatusBadge = ({
  data,
  fieldName = "status",
  className = "",
  t,
}) => {
  if (!data || typeof data[fieldName] === "undefined") return null;

  const rawStatus = String(data[fieldName]).trim().toLowerCase();
  const color =
    StatusColors[rawStatus] ||
    "bg-gray-100 text-gray-800 border border-gray-300";

  const label = t
    ? t(`status.${rawStatus}`) ||
      rawStatus.charAt(0).toUpperCase() + rawStatus.slice(1)
    : rawStatus.charAt(0).toUpperCase() + rawStatus.slice(1);

  return (
    <span
      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-md shadow w-20 justify-center items-center ${color} ${className}`}
    >
      {label}
    </span>
  );
};

export default ApprovalStatusBadge;
