'use client';

import { useEffect, useState } from 'react';
import useSWRCustom from '@/lib/useSWR';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import BlogSidebar from './BlogSidebar';
import BlogCard from './BlogCard';
import BlogPageSkeleton from './BlogPageSkeleton';

export default function BlogCategoryContent({ categorySlug }) {
  const [categories, setCategories] = useState([]);
  
  // Fetch category articles
  const { data: articlesResult, error: articlesError, isLoading: articlesLoading } = useSWRCustom(`/client/blogs/category/${categorySlug}`);
  
  // Fetch categories for sidebar
  const { data: categoriesResult } = useSWRCustom('/client/blogs/categories');

  useEffect(() => {
    if (categoriesResult?.data) {
      setCategories(categoriesResult.data);
    }
  }, [categoriesResult]);

  if (articlesError) {
    console.error('Failed to fetch category articles:', articlesError);
    return (
      <div className="container py-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Category not found</h2>
          <p className="text-gray-600">The category you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  if (articlesLoading || !articlesResult) {
    return <BlogPageSkeleton />;
  }

  const articles = articlesResult?.data?.data || [];
  const categoryInfo = categories.find(cat => cat.slug === categorySlug);
  const categoryName = categoryInfo?.name || categorySlug;

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Wellness Hub', href: '/blog' },
    { label: categoryName }
  ];

  return (
    <div className="container py-6">
      {/* Breadcrumbs */}
      <div className="mb-6">
        <Breadcrumb items={breadcrumbItems} />
      </div>

      {/* Category Header */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-rico-secondary-dark-1 mb-4">
          {categoryName}
        </h1>
        <p className="text-lg text-gray-600">
          Explore our collection of {categoryName.toLowerCase()} articles
        </p>
      </div>

      {/* Same Three-Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        
        {/* Left Sidebar - Same as blog landing */}
        <aside className="lg:col-span-1">
          <div className="sticky top-24 space-y-6">
            <BlogSidebar 
              categories={categories}
              featuredAuthors={[]}
              blogStats={{}}
            />
          </div>
        </aside>

        {/* Articles Grid */}
        <main className="lg:col-span-3">
          <div className="space-y-6">
            {/* Sort and Filter Controls */}
            <div className="flex items-center justify-between bg-white rounded-lg border border-gray-200 p-4">
              <p className="text-gray-600">{articles.length} articles found</p>
              <select className="border border-gray-300 rounded-md px-3 py-1 text-sm">
                <option value="newest">Newest First</option>
                <option value="popular">Most Popular</option>
                <option value="oldest">Oldest First</option>
              </select>
            </div>
            
            {/* Articles Grid */}
            {articles.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                {articles.map(article => (
                  <BlogCard 
                    key={article.id}
                    variant="compact"
                    article={article}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <h3 className="text-xl font-semibold text-gray-800 mb-2">No articles found</h3>
                <p className="text-gray-600">There are no articles in this category yet.</p>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}
