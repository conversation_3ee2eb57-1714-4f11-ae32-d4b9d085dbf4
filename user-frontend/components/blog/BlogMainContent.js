'use client';

import BlogSearchBar from './BlogSearchBar';
import BlogCard from './BlogCard';
import AuthorCard from './AuthorCard';
import SectionHeader from './SectionHeader';

export default function BlogMainContent({ 
  latestArticles = [], 
  popularArticles = [], 
  featuredAuthors = [] 
}) {
  return (
    <div className="space-y-8">
      {/* Search Bar */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <BlogSearchBar />
      </div>

      {/* What's New Section */}
      {latestArticles.length > 0 && (
        <section>
          <SectionHeader title="What's New" />
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
            {latestArticles.map(article => (
              <BlogCard key={article.id} article={article} variant="compact" />
            ))}
          </div>
        </section>
      )}

      {/* Popular Articles Section */}
      {popularArticles.length > 0 && (
        <section>
          <SectionHeader title="Popular Articles" />
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
            {popularArticles.map(article => (
              <BlogCard key={article.id} article={article} variant="compact" />
            ))}
          </div>
        </section>
      )}

      {/* Featured Authors Section */}
      {featuredAuthors.length > 0 && (
        <section>
          <SectionHeader title="Featured Authors" />
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
            {featuredAuthors.map(author => (
              <AuthorCard key={author.id} author={author} />
            ))}
          </div>
        </section>
      )}

      {/* Explore Topics Section */}
      <section>
        <SectionHeader title="Explore Topics" />
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { name: 'Nutrition', slug: 'nutrition', count: 25 },
            { name: 'Fitness', slug: 'fitness', count: 18 },
            { name: 'Wellness', slug: 'wellness', count: 32 },
            { name: 'Beauty', slug: 'beauty', count: 14 }
          ].map(topic => (
            <div 
              key={topic.slug}
              className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
            >
              <h3 className="font-semibold text-rico-secondary-dark-1 mb-1">
                {topic.name}
              </h3>
              <p className="text-sm text-gray-600">{topic.count} articles</p>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
