'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import useSWR<PERSON>ustom from '@/lib/useSWR';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import BlogSidebar from './BlogSidebar';
import BlogCard from './BlogCard';
import CategoryChip from './CategoryChip';
import AuthorBadge from './AuthorBadge';
import BlogPageSkeleton from './BlogPageSkeleton';
import { formatDate } from '@/lib/utils';

export default function BlogPostContent({ category, slug }) {
  const [categories, setCategories] = useState([]);
  
  // Fetch blog post data
  const { data: blogResult, error: blogError, isLoading: blogLoading } = useSWRCustom(`/client/blogs/${slug}`);
  
  // Fetch categories for sidebar
  const { data: categoriesResult } = useSWRCustom('/client/blogs/categories');

  useEffect(() => {
    if (categoriesResult?.data) {
      setCategories(categoriesResult.data);
    }
  }, [categoriesResult]);

  if (blogError) {
    console.error('Failed to fetch blog post:', blogError);
    return (
      <div className="container py-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Blog post not found</h2>
          <p className="text-gray-600">The article you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    );
  }

  if (blogLoading || !blogResult) {
    return <BlogPageSkeleton />;
  }

  const data = blogResult?.data;
  const article = data?.article;
  const relatedArticles = data?.related_articles || [];
  const authorInfo = data?.author_info;
  const categoryInfo = data?.category_info;

  if (!article) {
    return (
      <div className="container py-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Article not found</h2>
          <p className="text-gray-600">The article you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Wellness Hub', href: '/blog' },
    { label: categoryInfo?.name || category, href: `/blog/${category}` },
    { label: article.title }
  ];

  return (
    <div className="container py-6">
      {/* Breadcrumbs */}
      <div className="mb-6">
        <Breadcrumb items={breadcrumbItems} />
      </div>

      {/* Three-Column Layout for Article */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        
        {/* Left Sidebar - Same as blog landing */}
        <aside className="lg:col-span-1">
          <div className="sticky top-24 space-y-6">
            <BlogSidebar 
              categories={categories}
              featuredAuthors={[]}
              blogStats={{}}
            />
          </div>
        </aside>

        {/* Article Content */}
        <article className="lg:col-span-3">
          <div className="bg-white rounded-lg border border-gray-200 p-6 md:p-8">
            {/* Article Header */}
            <header className="mb-8">
              <CategoryChip category={article.category} className="mb-4" />
              <h1 className="text-3xl md:text-4xl font-bold text-rico-secondary-dark-1 mb-4">
                {article.title}
              </h1>
              {article.excerpt && (
                <p className="text-lg text-gray-600 mb-6">{article.excerpt}</p>
              )}
              
              <div className="flex flex-wrap items-center gap-4 mb-6">
                <AuthorBadge author={article.author} showCredentials />
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <time>{formatDate(article.published_at)}</time>
                  <span>•</span>
                  <span>{article.read_time} min read</span>
                  <span>•</span>
                  <span>{article.view_count} views</span>
                </div>
              </div>
            </header>

            {/* Cover Image */}
            {article.cover_image && (
              <div className="mb-8">
                <Image
                  src={article.cover_image}
                  alt={article.title}
                  width={800}
                  height={450}
                  className="w-full rounded-lg"
                />
              </div>
            )}

            {/* Article Content */}
            <div className="prose prose-lg max-w-none mb-8">
              <div dangerouslySetInnerHTML={{ __html: article.content }} />
            </div>

            {/* Author Bio */}
            {authorInfo && (
              <div className="mt-8 p-6 bg-rico-secondary-light-4 rounded-lg">
                <h3 className="font-semibold mb-4 text-rico-secondary-dark-1">About the Author</h3>
                <AuthorBadge author={authorInfo} showCredentials />
                <p className="text-sm text-gray-600 mt-2">
                  {authorInfo.article_count} articles published
                </p>
              </div>
            )}

            {/* Related Articles */}
            {relatedArticles.length > 0 && (
              <section className="mt-12">
                <h3 className="font-semibold mb-6 text-rico-secondary-dark-1">Related Articles</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {relatedArticles.map(relatedArticle => (
                    <BlogCard key={relatedArticle.id} article={relatedArticle} variant="compact" />
                  ))}
                </div>
              </section>
            )}
          </div>
        </article>
      </div>
    </div>
  );
}
