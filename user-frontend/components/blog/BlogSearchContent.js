'use client';

import { useEffect, useState } from 'react';
import useS<PERSON><PERSON>ustom from '@/lib/useSWR';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import BlogSidebar from './BlogSidebar';
import BlogCard from './BlogCard';
import BlogSearchBar from './BlogSearchBar';
import BlogPageSkeleton from './BlogPageSkeleton';

export default function BlogSearchContent({ searchQuery }) {
  const [categories, setCategories] = useState([]);
  
  // Fetch search results
  const { data: searchResult, error: searchError, isLoading: searchLoading } = useSWRCustom(
    searchQuery ? `/client/blogs/search?search=${encodeURIComponent(searchQuery)}` : null
  );
  
  // Fetch categories for sidebar
  const { data: categoriesResult } = useSWRCustom('/client/blogs/categories');

  useEffect(() => {
    if (categoriesResult?.data) {
      setCategories(categoriesResult.data);
    }
  }, [categoriesResult]);

  if (searchError) {
    console.error('Failed to fetch search results:', searchError);
    return (
      <div className="container py-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Search failed</h2>
          <p className="text-gray-600">There was an error performing your search. Please try again.</p>
        </div>
      </div>
    );
  }

  if (searchLoading) {
    return <BlogPageSkeleton />;
  }

  const articles = searchResult?.data?.data || [];

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Wellness Hub', href: '/blog' },
    { label: `Search: "${searchQuery}"` }
  ];

  return (
    <div className="container py-6">
      {/* Breadcrumbs */}
      <div className="mb-6">
        <Breadcrumb items={breadcrumbItems} />
      </div>

      {/* Search Header */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-rico-secondary-dark-1 mb-4">
          Search Results
        </h1>
        <p className="text-lg text-gray-600">
          {searchQuery ? `Results for "${searchQuery}"` : 'Enter a search term to find articles'}
        </p>
      </div>

      {/* Three-Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        
        {/* Left Sidebar */}
        <aside className="lg:col-span-1">
          <div className="sticky top-24 space-y-6">
            <BlogSidebar 
              categories={categories}
              featuredAuthors={[]}
              blogStats={{}}
            />
          </div>
        </aside>

        {/* Search Results */}
        <main className="lg:col-span-3">
          <div className="space-y-6">
            {/* Search Bar */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <BlogSearchBar />
            </div>

            {/* Results */}
            {searchQuery && (
              <>
                <div className="flex items-center justify-between bg-white rounded-lg border border-gray-200 p-4">
                  <p className="text-gray-600">
                    {articles.length} {articles.length === 1 ? 'result' : 'results'} found
                  </p>
                </div>
                
                {articles.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                    {articles.map(article => (
                      <BlogCard 
                        key={article.id}
                        variant="compact"
                        article={article}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">No results found</h3>
                    <p className="text-gray-600">
                      Try adjusting your search terms or browse our categories.
                    </p>
                  </div>
                )}
              </>
            )}

            {!searchQuery && (
              <div className="text-center py-12">
                <h3 className="text-xl font-semibold text-gray-800 mb-2">Start your search</h3>
                <p className="text-gray-600">
                  Enter keywords in the search bar above to find relevant articles.
                </p>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}
